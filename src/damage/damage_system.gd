extends Node

@export var health_system: HealthSystem

var _cooldowns: Dictionary = {}

func _physics_process(delta: float) -> void:
	_tick_cooldowns(delta)
	var dealers: Array[DamageDealerComponent] = _collect_dealers()
	for dealer: DamageDealerComponent in dealers:
		var dealer_data: DamageDealerData = dealer.data
		if dealer_data == null:
			continue
		var area: Area2D = _resolve_area(dealer, dealer_data.area_path)
		if area == null:
			continue
		var areas: Array[Area2D] = area.get_overlapping_areas()
		for a: Area2D in areas:
			_process_hit(dealer, a, dealer_data)

func _process_hit(dealer: DamageDealerComponent, target_node: Node, dealer_data: DamageDealerData) -> void:
	var health_component: HealthComponent = _find_health_component(target_node)
	if health_component == null:
		return
	var key: String = _pair_key(dealer, health_component)
	var left: float = _cooldowns.get(key, 0.0)
	if left > 0.0:
		return
	health_system.apply_damage(health_component.data, dealer_data.amount)
	_cooldowns[key] = dealer_data.cooldown
	if health_system.is_dead(health_component.data):
		_invoke_die(health_component)

func _resolve_area(dealer: DamageDealerComponent, path: NodePath) -> Area2D:
	if path.is_empty():
		return null
	var dealer_owner: Node = dealer.get_parent()
	if dealer_owner == null:
		return null
	var n: Node = dealer_owner.get_node_or_null(path)
	if n is Area2D:
		return n as Area2D
	return null

func _find_health_component(node: Node) -> HealthComponent:
	var current: Node = node
	while current != null:
		var children: Array[Node] = current.get_children()
		for c: Node in children:
			if c is HealthComponent:
				return c as HealthComponent
		current = current.get_parent()
	return null

func _collect_dealers() -> Array[DamageDealerComponent]:
	var result: Array[DamageDealerComponent] = []
	var stack: Array[Node] = [get_tree().root]
	while stack.size() > 0:
		var n: Node = stack.pop_back()
		var children: Array[Node] = n.get_children()
		for ch: Node in children:
			stack.push_back(ch)
			if ch is DamageDealerComponent:
				result.append(ch)
	return result

func _pair_key(dealer: DamageDealerComponent, health_component: HealthComponent) -> String:
	return str(dealer.get_instance_id()) + ":" + str(health_component.get_instance_id())

func _tick_cooldowns(delta: float) -> void:
	var keys: Array = _cooldowns.keys()
	for k: String in keys:
		var v: float = _cooldowns[k]
		v -= delta
		if v <= 0.0:
			_cooldowns.erase(k)
		else:
			_cooldowns[k] = v

func _invoke_die(health_component: HealthComponent) -> void:
	var owner_node: Node = health_component.get_parent() if health_component.get_parent() != null else health_component
	if owner_node == null:
		return
	if owner_node.is_queued_for_deletion():
		return
	if owner_node.has_method(&"die"):
		owner_node.call(&"die")
	else:
		push_warning("Узел не имеет метода 'die'")
