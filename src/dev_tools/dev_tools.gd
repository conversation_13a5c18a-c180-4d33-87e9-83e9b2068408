extends <PERSON>de2D

@export var paint_all_tiles_button: Button
@export var toggle_safe_zone_button: Button
@export var add_inverted_controls_button: Button

const DEBUG_INDICATOR_NAME: StringName = &"DebugSafeTileIndicator"
const SAFE_COLOR := Color(0.0, 1.0, 0.0, 0.9)
const UNSAFE_COLOR := Color(1.0, 0.0, 0.0, 0.9)

var level_progress_system: LevelProgressSystem
var _show_safe_zones: bool = false
var _tile_indicators: Dictionary = {}

func _ready() -> void:
	paint_all_tiles_button.pressed.connect(paint_all_tiles)
	toggle_safe_zone_button.pressed.connect(_on_toggle_safe_zone_pressed)
	add_inverted_controls_button.pressed.connect(_on_add_inverted_controls_pressed)
	_initialize_debug_system.call_deferred()

func _initialize_debug_system() -> void:
	level_progress_system = _find_level_progress_system()
	_create_indicators_for_all_tiles()

func _process(_delta: float) -> void:
	if not _show_safe_zones or not is_instance_valid(level_progress_system):
		return

	var safe_tiles_array: Array[Node2D] = level_progress_system.get_safe_tiles()
	var safe_tiles_set := {}
	for tile in safe_tiles_array:
		safe_tiles_set[tile] = true

	for tile_node: Node2D in _tile_indicators:
		var indicator: ColorRect = _tile_indicators[tile_node]
		if safe_tiles_set.has(tile_node):
			indicator.color = SAFE_COLOR
		else:
			indicator.color = UNSAFE_COLOR

func _on_add_inverted_controls_pressed() -> void:
	var player_node: Node = get_tree().get_first_node_in_group(&"player")
	if not is_instance_valid(player_node):
		return

	var ability_component: AbilityComponent = player_node.find_child(&"AbilityComponent", true, false)
	if not is_instance_valid(ability_component):
		return

	for ability: AbilityData in ability_component.abilities:
		if ability is InvertedControlsAbilityData:
			return

	var new_ability: InvertedControlsAbilityData = InvertedControlsAbilityData.new()
	ability_component.abilities.append(new_ability)

func _on_toggle_safe_zone_pressed() -> void:
	_show_safe_zones = not _show_safe_zones
	if _show_safe_zones:
		_show_all_indicators()
	else:
		_hide_all_indicators()

func _create_indicators_for_all_tiles() -> void:
	var all_tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	for tile in all_tiles:
		var tile_node: Node2D = tile as Node2D
		var indicator := ColorRect.new()
		indicator.name = DEBUG_INDICATOR_NAME
		indicator.size = Vector2(8, 8)
		indicator.position = Vector2(-4, -4)
		indicator.visible = false
		tile_node.add_child(indicator)
		_tile_indicators[tile_node] = indicator

func _show_all_indicators() -> void:
	for indicator: ColorRect in _tile_indicators.values():
		var canvas_item: CanvasItem = indicator
		canvas_item.visible = true

func _hide_all_indicators() -> void:
	for indicator: ColorRect in _tile_indicators.values():
		var canvas_item: CanvasItem = indicator
		canvas_item.visible = false

func _find_level_progress_system() -> LevelProgressSystem:
	var root: Node = get_tree().root
	return _search_for_type(root, LevelProgressSystem) as LevelProgressSystem

func _search_for_type(node: Node, type: Script) -> Node:
	if node.get_script() == type:
		return node

	for child in node.get_children():
		var result: Node = _search_for_type(child, type)
		if result != null:
			return result

	return null

func paint_all_tiles() -> void:
	var tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	for tile in tiles:
		var color_tile: ColorTile = tile as ColorTile
		color_tile.paint(Color.RED)
