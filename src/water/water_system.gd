class_name WaterSystem
extends Node

@export var tile_query_system: TileQuerySystem
@export var player_service: PlayerService

var _player: CharacterBody2D
var _paint_component: PaintComponent
var _movement_component: MovementComponent
var _hitbox: Area2D

var _is_underwater: bool = false
var _saved_collision_layer: int = 0
var _saved_monitorable: bool = true

func _ready() -> void:
	_player = player_service.get_player_node()
	_paint_component = player_service.get_paint_component()
	_movement_component = player_service.get_movement_component()
	_hitbox = player_service.get_hitbox()

	_movement_component.data.movement_completed.connect(_on_move_completed)
	_update_state()

func _on_move_completed() -> void:
	_update_state()
	_apply_paint_cost()

func _apply_paint_cost() -> void:
	if not _is_underwater:
		return
	var tile_component: WaterTileComponent = _current_water_tile()
	if not is_instance_valid(tile_component):
		return
	var paint_data: PaintData = _paint_component.data
	if paint_data.current_paint > 0:
		paint_data.current_paint -= tile_component.data.paint_cost

func _update_state() -> void:
	var was_underwater: bool = _is_underwater
	_is_underwater = is_instance_valid(_current_water_tile())
	if was_underwater == _is_underwater:
		return
	if _is_underwater:
		_saved_collision_layer = _player.collision_layer
		_player.collision_layer = 0
		_saved_monitorable = _hitbox.monitorable
		_hitbox.monitorable = false
	else:
		_player.collision_layer = _saved_collision_layer
		_hitbox.monitorable = _saved_monitorable

func _current_water_tile() -> WaterTileComponent:
	var tile: Node2D = tile_query_system.get_tile_at_global_pos(_player.global_position)
	if not is_instance_valid(tile) or tile is not WaterTileComponent:
		return null
	return tile
