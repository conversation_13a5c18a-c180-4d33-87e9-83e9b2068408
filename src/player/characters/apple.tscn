[gd_scene load_steps=10 format=3 uid="uid://cxhc4tig2eklx"]

[ext_resource type="PackedScene" uid="uid://dq0bjiqkyl4wv" path="res://src/player/player.tscn" id="1_3jrxr"]
[ext_resource type="Script" uid="uid://chn55a5u5s7ol" path="res://src/health/health_data.gd" id="2_fnuva"]
[ext_resource type="Script" uid="uid://s041hcolx1gq" path="res://src/paint/paint_data.gd" id="3_3w74p"]
[ext_resource type="Script" uid="uid://72hm77eoq5jd" path="res://src/ability/ability_data.gd" id="4_t4m1h"]
[ext_resource type="Script" uid="uid://bkusl4o508on1" path="res://src/ability/multi_drop_ability_data.gd" id="5_m1yns"]
[ext_resource type="Texture2D" uid="uid://b0kc3eu271qxd" path="res://assets/apple.png" id="6_m1yns"]

[sub_resource type="Resource" id="Resource_3w74p"]
script = ExtResource("2_fnuva")
max_health = 1
current_health = 1
metadata/_custom_type_script = "uid://chn55a5u5s7ol"

[sub_resource type="Resource" id="Resource_ihfri"]
script = ExtResource("3_3w74p")
paint_color = Color(1, 0.466667, 0.662745, 1)
max_paint = 10
metadata/_custom_type_script = "uid://s041hcolx1gq"

[sub_resource type="Resource" id="Resource_kkndx"]
script = ExtResource("5_m1yns")
drop_count = 3
metadata/_custom_type_script = "uid://bkusl4o508on1"

[node name="Apple" instance=ExtResource("1_3jrxr")]

[node name="HealthComponent" parent="." index="0"]
data = SubResource("Resource_3w74p")

[node name="PaintComponent" parent="." index="1"]
data = SubResource("Resource_ihfri")

[node name="AbilityComponent" parent="." index="2"]
abilities = Array[ExtResource("4_t4m1h")]([SubResource("Resource_kkndx")])

[node name="Sprite2D" parent="." index="5"]
texture = ExtResource("6_m1yns")

[node name="Collision" parent="." index="6"]
position = Vector2(0, -4)

[node name="Hitbox" parent="." index="7"]
position = Vector2(-1, -4)
