[gd_scene load_steps=9 format=3 uid="uid://c1mrsuk0j1dqe"]

[ext_resource type="PackedScene" uid="uid://dq0bjiqkyl4wv" path="res://src/player/player.tscn" id="1_jf61d"]
[ext_resource type="Script" uid="uid://chn55a5u5s7ol" path="res://src/health/health_data.gd" id="2_xurc4"]
[ext_resource type="Script" uid="uid://s041hcolx1gq" path="res://src/paint/paint_data.gd" id="3_3xjrr"]
[ext_resource type="Script" uid="uid://72hm77eoq5jd" path="res://src/ability/ability_data.gd" id="4_5hrh8"]
[ext_resource type="Script" uid="uid://b5xc2p1kg3ho6" path="res://src/ability/wall_phase_ability_data.gd" id="7_fwkl5"]

[sub_resource type="Resource" id="Resource_3xjrr"]
script = ExtResource("2_xurc4")
max_health = 1
current_health = 1
metadata/_custom_type_script = "uid://chn55a5u5s7ol"

[sub_resource type="Resource" id="Resource_s2dbu"]
script = ExtResource("3_3xjrr")
paint_color = Color(1, 0.914, 0.133, 1)
max_paint = 10
metadata/_custom_type_script = "uid://s041hcolx1gq"

[sub_resource type="Resource" id="Resource_nlfcc"]
script = ExtResource("7_fwkl5")
paint_cost = 1
metadata/_custom_type_script = "uid://b5xc2p1kg3ho6"

[node name="Banana" instance=ExtResource("1_jf61d")]

[node name="HealthComponent" parent="." index="0"]
data = SubResource("Resource_3xjrr")

[node name="PaintComponent" parent="." index="1"]
data = SubResource("Resource_s2dbu")

[node name="AbilityComponent" parent="." index="2"]
abilities = Array[ExtResource("4_5hrh8")]([SubResource("Resource_nlfcc")])
