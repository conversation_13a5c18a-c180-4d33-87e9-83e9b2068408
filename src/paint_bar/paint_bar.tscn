[gd_scene load_steps=4 format=3 uid="uid://ce5u8lunfmkij"]

[ext_resource type="Texture2D" uid="uid://croipc1prk67w" path="res://assets/paint_bar_texture.png" id="1_nbdyy"]
[ext_resource type="Texture2D" uid="uid://biagw5gvp674n" path="res://assets/color_drop.png" id="2_lvah4"]
[ext_resource type="Script" uid="uid://cegt7qtr3ttwp" path="res://src/paint_bar/paint_bar.gd" id="3_paint_bar"]

[node name="PaintBar" type="Node2D" node_paths=PackedStringArray("drops_container", "frame")]
script = ExtResource("3_paint_bar")
drops_container = NodePath("NinePatchRect/MarginContainer/HBoxContainer")
frame = NodePath("NinePatchRect")

[node name="NinePatchRect" type="NinePatchRect" parent="."]
texture_filter = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -36.0
offset_top = -4.0
offset_right = 36.0
offset_bottom = 4.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("1_nbdyy")
region_rect = Rect2(0, 0, 8, 8)
patch_margin_left = 1
patch_margin_top = 1
patch_margin_right = 1
patch_margin_bottom = 1

[node name="MarginContainer" type="MarginContainer" parent="NinePatchRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 2
theme_override_constants/margin_top = 2
theme_override_constants/margin_right = 2
theme_override_constants/margin_bottom = 2

[node name="HBoxContainer" type="HBoxContainer" parent="NinePatchRect/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 2

[node name="ColorRect" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect2" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect3" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect4" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect5" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect6" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect7" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect8" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect9" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect10" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect11" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect12" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorRect13" type="ColorRect" parent="NinePatchRect/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(1.5, 0)
layout_mode = 2
color = Color(1, 0.913725, 0.133333, 1)

[node name="ColorDrop" type="Sprite2D" parent="NinePatchRect"]
texture_filter = 1
position = Vector2(-7, 4)
texture = ExtResource("2_lvah4")
