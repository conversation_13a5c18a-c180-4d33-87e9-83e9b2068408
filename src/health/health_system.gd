class_name HealthSystem
extends Node

func set_health(data: HealthData, value: int) -> void:
	var v: int = value
	if v < 0:
		v = 0
	if v > data.max_health:
		v = data.max_health
	data.current_health = v

func apply_damage(data: HealthData, amount: int) -> void:
	set_health(data, data.current_health - amount)

func heal(data: HealthData, amount: int) -> void:
	set_health(data, data.current_health + amount)

func is_dead(data: HealthData) -> bool:
	return data.current_health <= 0
