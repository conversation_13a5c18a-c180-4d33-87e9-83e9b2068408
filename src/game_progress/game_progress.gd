extends Node

const SAVE_PATH = "user://progress.dat"

var _completed_levels: Dictionary = {}

func _ready() -> void:
	load_progress()

func is_level_completed(level_id: String) -> bool:
	return _completed_levels.has(level_id)

func mark_level_as_completed(level_id: String) -> void:
	if not level_id.is_empty():
		_completed_levels[level_id] = true
		save_progress()

func save_progress() -> void:
	var file: FileAccess = FileAccess.open(SAVE_PATH, FileAccess.WRITE)
	if file:
		var json_string: String = JSON.stringify(_completed_levels)
		file.store_string(json_string)

func load_progress() -> void:
	if not FileAccess.file_exists(SAVE_PATH):
		return

	var file: FileAccess = FileAccess.open(SAVE_PATH, FileAccess.READ)
	if file:
		var json_string: String = file.get_as_text()
		var parse_result: Variant = JSON.parse_string(json_string)
		if parse_result is Dictionary:
			_completed_levels = parse_result
