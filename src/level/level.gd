extends Node2D

@export var level_id: String = ""
@export var level_state_component: LevelStateComponent
@export var win_ui: CanvasLayer
@export var loss_ui: CanvasLayer
@export var tile_query_system: TileQuerySystem

func _ready() -> void:
	_setup_level.call_deferred()
	level_state_component.data.state_changed.connect(_on_level_state_changed)

func _setup_level() -> void:
	var tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	level_state_component.data.total_tiles = tiles.size()

	for tile_node: ColorTile in tiles:
		if tile_node is ColorTile:
			tile_node.painted.connect(_on_tile_painted)


func _on_level_state_changed(new_state: LevelStateData.State) -> void:
	if new_state == LevelStateData.State.WON:
		win_ui.show()
	elif new_state == LevelStateData.State.LOST:
		loss_ui.show()

func _on_tile_painted() -> void:
	level_state_component.data.painted_tiles += 1
