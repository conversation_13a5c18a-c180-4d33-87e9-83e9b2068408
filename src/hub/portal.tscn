[gd_scene load_steps=4 format=3 uid="uid://c8lam3n4p5q6r"]

[ext_resource type="Script" path="res://src/hub/portal.gd" id="1_portal"]
[ext_resource type="Texture2D" uid="uid://da3x0kkx4t6dj" path="res://icon.svg" id="2_icon"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(8, 8)

[node name="Portal" type="Area2D"]
script = ExtResource("1_portal")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.5, 0.8, 1, 0.8)
texture_filter = 1
texture = ExtResource("2_icon")
scale = Vector2(0.1, 0.1)
