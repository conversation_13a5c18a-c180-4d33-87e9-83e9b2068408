extends Node

var run_levels: Array[String] = [
	"res://src/levels/level_1/level_1_1.tscn",
	"res://src/levels/level_1/level_1_2.tscn",
	"res://src/levels/level_1/level_1_3.tscn",
	"res://src/levels/level_1/level_1_4.tscn"
]

func start_run() -> void:
	if run_levels.is_empty():
		push_error("GameOrchestrator: No run levels configured")
		return

	var selected_level: String = run_levels[0]
	get_tree().change_scene_to_file.call_deferred(selected_level)

func go_to_hub() -> void:
	get_tree().change_scene_to_file.call_deferred("res://src/hub/hub.tscn")
