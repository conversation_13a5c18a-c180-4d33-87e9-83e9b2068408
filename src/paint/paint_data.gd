class_name PaintData
extends Resource

signal paint_changed(current_paint: int, max_paint: int)

@export var paint_color: Color = Color.WHITE
@export var max_paint: int = 10

var current_paint: int:
	set(new_value):
		var clamped_value: int = clamp(new_value, 0, max_paint)
		if current_paint == clamped_value:
			return
		current_paint = clamped_value
		paint_changed.emit(current_paint, max_paint)

func reset() -> void:
	current_paint = max_paint
