[gd_scene load_steps=3 format=3 uid="uid://paint_component_uid"]

[ext_resource type="Script" path="res://src/paint/paint_component.gd" id="1_paint_component"]
[ext_resource type="Script" path="res://src/paint/paint_data.gd" id="2_paint_data"]

[sub_resource type="Resource" id="PaintData_resource"]
script = ExtResource("2_paint_data")
max_paint = 10
current_paint = 10
metadata/_custom_type_script = "uid://paint_data_uid"

[node name="PaintComponent" type="Node"]
script = ExtResource("1_paint_component")
data = SubResource("PaintData_resource")
